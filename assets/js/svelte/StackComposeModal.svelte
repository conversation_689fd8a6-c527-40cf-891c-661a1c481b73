<script lang="ts">
	import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from '@sveltestrap/sveltestrap';
	import { getStackCompose } from '../api';

	interface Props {
		service: Service | null;
		isOpen?: boolean;
		toggle?: any;
	}

	let { service, isOpen = $bindable(false), toggle = () => (isOpen = !isOpen) }: Props = $props();

	let composeContent = $state('');
	let loading = $state(false);
	let error = $state(false);

	async function fetchStackCompose() {
		if (!service) return;
		
		loading = true;
		error = false;
		composeContent = '';

		try {
			const result = await getStackCompose(service.server, service.stack);
			composeContent = result;
		} catch (e) {
			error = true;
			console.error('Erreur lors de la récupération du fichier compose:', e);
		}

		loading = false;
	}

	async function onOpening() {
		composeContent = '';
		error = false;
	}

	async function onOpen() {
		await fetchStackCompose();
	}
</script>

{#if service}
	<Modal {isOpen} {toggle} size="xl" on:open={onOpen} on:opening={onOpening}>
		<ModalHeader {toggle}>
			Fichier compose - {service.server} | {service.stack}
		</ModalHeader>
		<ModalBody>
			{#if loading}
				<div class="text-center">
					<div class="spinner-border text-primary" role="status">
						<span class="visually-hidden">Loading...</span>
					</div>
					<p class="mt-2">Chargement du fichier compose de la stack {service.stack}...</p>
				</div>
			{:else if error}
				<div class="alert alert-danger d-flex align-items-center" role="alert">
					<i class="fa-solid fa-circle-exclamation me-2"></i>
					Impossible de récupérer le fichier compose de la stack {service.stack}
				</div>
			{:else if composeContent}
				<div class="d-flex justify-content-between align-items-center mb-3">
					<h6 class="mb-0">Contenu du fichier compose.yml</h6>
				</div>
                <p class="fst-italic">Attention il s'agit de la version compilée par Docker, pas du vrai contenu du fichier</p>
				<pre class="bg-light p-3 rounded" style="overflow-y: auto;"><code>{composeContent}</code></pre>
			{/if}
		</ModalBody>
		<ModalFooter>
			<Button color="secondary" on:click={toggle}>Fermer</Button>
		</ModalFooter>
	</Modal>
{/if}
